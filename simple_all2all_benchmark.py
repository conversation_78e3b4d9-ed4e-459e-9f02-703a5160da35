#!/usr/bin/env python3
"""
简化版性能测试：all_to_all_single vs all_gather + reduce_scatter
运行命令：torchrun --nproc_per_node=4 simple_all2all_benchmark.py
"""

import torch
import torch.distributed as dist
import time
import os


def setup_distributed():
    """初始化分布式环境"""
    if not dist.is_initialized():
        dist.init_process_group(backend='nccl' if torch.cuda.is_available() else 'gloo')
    
    rank = dist.get_rank()
    world_size = dist.get_world_size()
    
    if torch.cuda.is_available():
        torch.cuda.set_device(rank % torch.cuda.device_count())
        device = torch.device(f'cuda:{rank % torch.cuda.device_count()}')
    else:
        device = torch.device('cpu')
    
    return rank, world_size, device


def benchmark_all_to_all_single(input_tensor, world_size, group, iterations=50):
    """测试 all_to_all_single 性能"""
    # 每个rank发送相同大小的数据
    input_size = input_tensor.size(0)
    output_tensor = torch.empty(
        input_size * world_size, input_tensor.size(1),
        dtype=input_tensor.dtype, device=input_tensor.device
    )
    
    # 预热
    for _ in range(10):
        dist.all_to_all_single(
            output_tensor, input_tensor,
            group=group
        )
    
    dist.barrier(group)
    
    # 性能测试
    start_time = time.perf_counter()
    for _ in range(iterations):
        dist.all_to_all_single(
            output_tensor, input_tensor,
            group=group
        )
    
    dist.barrier(group)
    end_time = time.perf_counter()
    
    return (end_time - start_time) / iterations


def benchmark_all_gather_then_split(input_tensor, world_size, group, iterations=50):
    """测试 all_gather + split 性能（模拟all_gather + reduce_scatter的通信开销）"""
    # 预热
    for _ in range(10):
        # All_gather
        tensor_list = [torch.empty_like(input_tensor) for _ in range(world_size)]
        dist.all_gather(tensor_list, input_tensor, group=group)
        
        # 模拟数据处理（简单的concatenate和split）
        concatenated = torch.cat(tensor_list, dim=0)
        # 简单split来模拟reduce_scatter的数据重组
        chunks = torch.chunk(concatenated, world_size, dim=0)
        result = chunks[0].clone()  # 模拟取一个chunk
    
    dist.barrier(group)
    
    # 性能测试
    start_time = time.perf_counter()
    for _ in range(iterations):
        # All_gather
        tensor_list = [torch.empty_like(input_tensor) for _ in range(world_size)]
        dist.all_gather(tensor_list, input_tensor, group=group)
        
        # 模拟数据处理
        concatenated = torch.cat(tensor_list, dim=0)
        chunks = torch.chunk(concatenated, world_size, dim=0)
        result = chunks[0].clone()
    
    dist.barrier(group)
    end_time = time.perf_counter()
    
    return (end_time - start_time) / iterations


def run_benchmark():
    """运行性能测试"""
    rank, world_size, device = setup_distributed()
    
    # 测试不同的数据大小
    test_sizes = [
        (512, 1024),    # 0.5M elements
        (1024, 2048),   # 2M elements  
        (2048, 4096),   # 8M elements
        (4096, 4096),   # 16M elements
    ]
    
    if rank == 0:
        print(f"{'='*80}")
        print(f"All2All vs All_gather+Split 性能对比测试")
        print(f"Backend: {dist.get_backend()}")
        print(f"World Size: {world_size}")
        print(f"Device: {device}")
        print(f"{'='*80}")
        print(f"{'Size':<15} {'All2All(ms)':<12} {'All_gather(ms)':<15} {'Speedup':<10} {'Bandwidth(GB/s)':<15}")
        print(f"{'-'*80}")
    
    for seq_len, hidden_dim in test_sizes:
        # 创建测试数据
        input_tensor = torch.randn(seq_len, hidden_dim, device=device, dtype=torch.float16)
        
        # 测试 all_to_all_single
        all2all_time = benchmark_all_to_all_single(input_tensor, world_size, dist.group.WORLD)
        
        # 测试 all_gather + split
        allgather_time = benchmark_all_gather_then_split(input_tensor, world_size, dist.group.WORLD)
        
        if rank == 0:
            # 计算数据大小和带宽
            data_size_mb = input_tensor.numel() * input_tensor.element_size() * world_size / (1024 * 1024)
            speedup = allgather_time / all2all_time
            bandwidth_all2all = data_size_mb / (all2all_time * 1000)  # GB/s
            
            size_str = f"{seq_len}x{hidden_dim}"
            print(f"{size_str:<15} {all2all_time*1000:<12.3f} {allgather_time*1000:<15.3f} {speedup:<10.2f} {bandwidth_all2all:<15.2f}")
    
    if rank == 0:
        print(f"{'-'*80}")
        print("注意：这是简化测试，实际性能可能因硬件和网络配置而异")


def test_ucc_availability():
    """测试UCC后端是否可用"""
    rank, world_size, device = setup_distributed()
    
    if rank == 0:
        print(f"\n{'='*50}")
        print("UCC 后端可用性测试")
        print(f"{'='*50}")
    
    # 检查UCC是否可用
    ucc_available = False
    try:
        if hasattr(dist, 'Backend') and hasattr(dist.Backend, 'UCC'):
            # 尝试创建UCC进程组
            ucc_group = dist.new_group(backend='ucc')
            ucc_available = True
            
            if rank == 0:
                print("✅ UCC 后端可用")
                
            # 简单测试UCC性能
            input_tensor = torch.randn(1024, 2048, device=device, dtype=torch.float16)
            ucc_time = benchmark_all_to_all_single(input_tensor, world_size, ucc_group, iterations=20)
            
            if rank == 0:
                print(f"UCC All2All 延迟: {ucc_time*1000:.3f} ms")
                
        else:
            if rank == 0:
                print("❌ UCC 后端不可用")
                
    except Exception as e:
        if rank == 0:
            print(f"❌ UCC 测试失败: {e}")
    
    return ucc_available


def main():
    try:
        # 基础性能测试
        run_benchmark()
        
        # UCC可用性测试
        test_ucc_availability()
        
    except Exception as e:
        rank = dist.get_rank() if dist.is_initialized() else 0
        if rank == 0:
            print(f"测试失败: {e}")
            import traceback
            traceback.print_exc()
    finally:
        if dist.is_initialized():
            dist.destroy_process_group()


if __name__ == "__main__":
    main()
