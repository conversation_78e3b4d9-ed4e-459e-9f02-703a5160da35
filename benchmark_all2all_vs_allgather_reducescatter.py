#!/usr/bin/env python3
"""
性能测试：all_to_all_single vs all_gather + reduce_scatter
需要在多进程环境中运行：torchrun --nproc_per_node=4 benchmark_all2all_vs_allgather_reducescatter.py
"""

import torch
import torch.distributed as dist
import time
import argparse
from typing import List


def setup_distributed():
    """初始化分布式环境"""
    if not dist.is_initialized():
        dist.init_process_group(backend='nccl' if torch.cuda.is_available() else 'gloo')
    
    rank = dist.get_rank()
    world_size = dist.get_world_size()
    
    if torch.cuda.is_available():
        torch.cuda.set_device(rank % torch.cuda.device_count())
        device = torch.device(f'cuda:{rank % torch.cuda.device_count()}')
    else:
        device = torch.device('cpu')
    
    return rank, world_size, device


def benchmark_all_to_all_single(input_tensor: torch.Tensor, 
                                chunk_sizes: List[int], 
                                group, 
                                warmup_iters: int = 10,
                                benchmark_iters: int = 100) -> float:
    """测试 all_to_all_single 性能"""
    world_size = dist.get_world_size(group)
    total_tokens = sum(chunk_sizes)
    
    output_tensor = torch.empty(
        (total_tokens, input_tensor.size(1)),
        dtype=input_tensor.dtype,
        device=input_tensor.device
    )
    
    # 预热
    for _ in range(warmup_iters):
        dist.all_to_all_single(
            output_tensor,
            input_tensor,
            output_split_sizes=chunk_sizes,
            input_split_sizes=[input_tensor.size(0)] * world_size,
            group=group
        )
    
    # 同步所有进程
    dist.barrier(group)
    
    # 性能测试
    start_time = time.perf_counter()
    for _ in range(benchmark_iters):
        dist.all_to_all_single(
            output_tensor,
            input_tensor,
            output_split_sizes=chunk_sizes,
            input_split_sizes=[input_tensor.size(0)] * world_size,
            group=group
        )
    
    dist.barrier(group)
    end_time = time.perf_counter()
    
    avg_time = (end_time - start_time) / benchmark_iters
    return avg_time


def benchmark_all_gather_reduce_scatter(input_tensor: torch.Tensor,
                                       chunk_sizes: List[int],
                                       group,
                                       warmup_iters: int = 10,
                                       benchmark_iters: int = 100) -> float:
    """测试 all_gather + reduce_scatter 性能"""
    world_size = dist.get_world_size(group)
    rank = dist.get_rank(group)
    
    # 预热
    for _ in range(warmup_iters):
        # All_gather
        tensor_list = [torch.empty_like(input_tensor) for _ in range(world_size)]
        dist.all_gather(tensor_list, input_tensor, group=group)
        concatenated = torch.cat(tensor_list, dim=0)
        
        # Reduce_scatter (这里简化为split，实际应该有reduce操作)
        chunks = torch.split(concatenated, chunk_sizes, dim=0)
        output = chunks[rank].clone()
    
    # 同步所有进程
    dist.barrier(group)
    
    # 性能测试
    start_time = time.perf_counter()
    for _ in range(benchmark_iters):
        # All_gather
        tensor_list = [torch.empty_like(input_tensor) for _ in range(world_size)]
        dist.all_gather(tensor_list, input_tensor, group=group)
        concatenated = torch.cat(tensor_list, dim=0)
        
        # Reduce_scatter (简化版本)
        chunks = torch.split(concatenated, chunk_sizes, dim=0)
        output = chunks[rank].clone()
    
    dist.barrier(group)
    end_time = time.perf_counter()
    
    avg_time = (end_time - start_time) / benchmark_iters
    return avg_time


def run_benchmark(tensor_size: int, hidden_dim: int, backend: str = 'nccl'):
    """运行完整的性能测试"""
    rank, world_size, device = setup_distributed()
    
    # 创建测试数据
    input_tensor = torch.randn(tensor_size, hidden_dim, device=device, dtype=torch.float16)
    
    # 模拟不均匀的chunk sizes
    base_chunk = tensor_size // world_size
    chunk_sizes = [base_chunk + (i % 2) * 10 for i in range(world_size)]
    
    # 创建进程组
    if backend == 'ucc' and hasattr(dist.Backend, 'UCC'):
        try:
            group = dist.new_group(backend='ucc')
            backend_name = 'UCC'
        except:
            group = dist.group.WORLD
            backend_name = 'Default'
    else:
        group = dist.group.WORLD
        backend_name = dist.get_backend()
    
    if rank == 0:
        print(f"\n{'='*60}")
        print(f"性能测试配置:")
        print(f"  Backend: {backend_name}")
        print(f"  World Size: {world_size}")
        print(f"  Tensor Size: {tensor_size} x {hidden_dim}")
        print(f"  Device: {device}")
        print(f"  Chunk Sizes: {chunk_sizes}")
        print(f"  Data Type: {input_tensor.dtype}")
        print(f"  Total Data: {input_tensor.numel() * input_tensor.element_size() / 1024 / 1024:.2f} MB")
        print(f"{'='*60}")
    
    # 测试 all_to_all_single
    all2all_time = benchmark_all_to_all_single(input_tensor, chunk_sizes, group)
    
    # 测试 all_gather + reduce_scatter
    allgather_reducescatter_time = benchmark_all_gather_reduce_scatter(input_tensor, chunk_sizes, group)
    
    if rank == 0:
        print(f"\n性能测试结果:")
        print(f"  All_to_all_single:           {all2all_time*1000:.3f} ms")
        print(f"  All_gather + Reduce_scatter: {allgather_reducescatter_time*1000:.3f} ms")
        print(f"  性能提升:                    {allgather_reducescatter_time/all2all_time:.2f}x")
        print(f"  延迟减少:                    {(allgather_reducescatter_time-all2all_time)*1000:.3f} ms")
        
        # 带宽分析
        data_size_mb = input_tensor.numel() * input_tensor.element_size() / 1024 / 1024
        all2all_bandwidth = data_size_mb / all2all_time
        combined_bandwidth = data_size_mb / allgather_reducescatter_time
        
        print(f"\n带宽分析:")
        print(f"  All_to_all_single 带宽:     {all2all_bandwidth:.2f} MB/s")
        print(f"  Combined 方法带宽:           {combined_bandwidth:.2f} MB/s")
        print(f"  带宽效率提升:                {all2all_bandwidth/combined_bandwidth:.2f}x")


def main():
    parser = argparse.ArgumentParser(description='All2All vs All_gather+Reduce_scatter 性能测试')
    parser.add_argument('--tensor-size', type=int, default=1024, help='每个rank的tensor大小')
    parser.add_argument('--hidden-dim', type=int, default=4096, help='隐藏层维度')
    parser.add_argument('--backend', type=str, default='nccl', choices=['nccl', 'gloo', 'ucc'], 
                       help='通信后端')
    
    args = parser.parse_args()
    
    try:
        run_benchmark(args.tensor_size, args.hidden_dim, args.backend)
    except Exception as e:
        rank = dist.get_rank() if dist.is_initialized() else 0
        if rank == 0:
            print(f"测试失败: {e}")
    finally:
        if dist.is_initialized():
            dist.destroy_process_group()


if __name__ == "__main__":
    main()
