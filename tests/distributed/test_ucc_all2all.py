# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project
"""Tests for UCC All2All functionality."""

import pytest
import torch
import torch.distributed as dist

from vllm.distributed.device_communicators.all2all import UCCAll2AllManager
from vllm.envs import VLLM_ALL2ALL_BACKEND


class TestUCCAll2AllManager:
    """Test suite for UCC All2All Manager."""

    def test_ucc_availability_check(self):
        """Test that UCC availability is properly checked."""
        # This test can run without distributed setup
        if not dist.is_ucc_available():
            pytest.skip("UCC backend is not available in this PyTorch build")
        
        # If UCC is available, we should be able to import the manager
        from vllm.distributed.device_communicators.all2all import UCCAll2AllManager
        assert UCCAll2AllManager is not None

    @pytest.mark.skipif(not dist.is_ucc_available(), 
                       reason="UCC backend not available")
    def test_ucc_manager_initialization_without_group(self):
        """Test UCC manager initialization behavior without process group."""
        # This test verifies that the manager handles missing process group gracefully
        try:
            # This should fail gracefully since we don't have a distributed setup
            manager = UCCAll2AllManager(None)
            assert False, "Should have failed without proper process group"
        except (RuntimeError, AttributeError, TypeError) as e:
            # Expected to fail without proper distributed setup
            assert True

    def test_backend_configuration(self):
        """Test that UCC backend is properly configured in environment variables."""
        from vllm.envs import VLLM_ALL2ALL_BACKEND
        
        # Check that 'ucc' is in the allowed choices
        # We can't directly access the choices, but we can test by setting the env var
        import os
        original_backend = os.environ.get('VLLM_ALL2ALL_BACKEND', None)
        
        try:
            # Set UCC backend
            os.environ['VLLM_ALL2ALL_BACKEND'] = 'ucc'
            
            # Re-import to get updated value
            import importlib
            import vllm.envs
            importlib.reload(vllm.envs)
            
            # This should not raise an error if 'ucc' is a valid choice
            backend = vllm.envs.VLLM_ALL2ALL_BACKEND
            assert backend == 'ucc'
            
        finally:
            # Restore original value
            if original_backend is not None:
                os.environ['VLLM_ALL2ALL_BACKEND'] = original_backend
            else:
                os.environ.pop('VLLM_ALL2ALL_BACKEND', None)
            
            # Reload to restore original state
            importlib.reload(vllm.envs)

    def test_communicator_integration(self):
        """Test that UCC manager is properly integrated into communicators."""
        from vllm.distributed.device_communicators.cuda_communicator import CudaCommunicator
        from vllm.distributed.device_communicators.xpu_communicator import XpuCommunicator
        
        # Check that the import statements are present in the communicators
        import inspect
        
        # Get source code of CudaCommunicator.__init__
        cuda_source = inspect.getsource(CudaCommunicator.__init__)
        assert 'UCCAll2AllManager' in cuda_source, "CudaCommunicator should import UCCAll2AllManager"
        assert 'ucc' in cuda_source, "CudaCommunicator should handle 'ucc' backend"
        
        # Get source code of XpuCommunicator.__init__
        xpu_source = inspect.getsource(XpuCommunicator.__init__)
        assert 'UCCAll2AllManager' in xpu_source, "XpuCommunicator should import UCCAll2AllManager"
        assert 'ucc' in xpu_source, "XpuCommunicator should handle 'ucc' backend"


@pytest.mark.distributed
@pytest.mark.skipif(not dist.is_ucc_available(), 
                   reason="UCC backend not available")
class TestUCCAll2AllDistributed:
    """Distributed tests for UCC All2All Manager.
    
    These tests require a distributed setup and should be run with torchrun.
    """

    def test_ucc_manager_creation_with_group(self):
        """Test UCC manager creation with proper process group."""
        if not dist.is_initialized():
            pytest.skip("Distributed not initialized")
        
        # Create a simple process group for testing
        world_size = dist.get_world_size()
        if world_size < 2:
            pytest.skip("Need at least 2 processes for distributed test")
        
        # Create a CPU group
        cpu_group = dist.new_group(backend="gloo")
        
        try:
            # This should work with proper distributed setup
            manager = UCCAll2AllManager(cpu_group)
            assert manager is not None
            assert hasattr(manager, 'ucc_group')
            
            # Test cleanup
            manager.destroy()
            
        except RuntimeError as e:
            if "UCC backend is not available" in str(e):
                pytest.skip("UCC backend not properly configured")
            else:
                raise

    def test_ucc_dispatch_combine_interface(self):
        """Test that UCC manager implements the required interface."""
        if not dist.is_initialized():
            pytest.skip("Distributed not initialized")
        
        world_size = dist.get_world_size()
        if world_size < 2:
            pytest.skip("Need at least 2 processes for distributed test")
        
        cpu_group = dist.new_group(backend="gloo")
        
        try:
            manager = UCCAll2AllManager(cpu_group)
            
            # Check that required methods exist
            assert hasattr(manager, 'dispatch')
            assert hasattr(manager, 'combine')
            assert hasattr(manager, 'destroy')
            
            # Check method signatures
            import inspect
            dispatch_sig = inspect.signature(manager.dispatch)
            assert len(dispatch_sig.parameters) == 2  # hidden_states, router_logits
            
            combine_sig = inspect.signature(manager.combine)
            assert len(combine_sig.parameters) == 1  # hidden_states
            
            manager.destroy()
            
        except RuntimeError as e:
            if "UCC backend is not available" in str(e):
                pytest.skip("UCC backend not properly configured")
            else:
                raise


if __name__ == "__main__":
    # Run basic tests
    test_instance = TestUCCAll2AllManager()
    test_instance.test_ucc_availability_check()
    test_instance.test_backend_configuration()
    test_instance.test_communicator_integration()
    print("Basic UCC All2All tests passed!")
