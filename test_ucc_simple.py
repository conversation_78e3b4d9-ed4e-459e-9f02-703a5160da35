#!/usr/bin/env python3
"""Simple test script for UCC All2All functionality without pytest dependency."""

import sys
import os

# Add the vllm directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_ucc_imports():
    """Test that UCC manager can be imported."""
    try:
        from vllm.distributed.device_communicators.all2all import UCCAll2AllManager
        print("✓ UCCAll2AllManager imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import UCCAll2AllManager: {e}")
        return False

def test_torch_ucc_availability():
    """Test if PyTorch has UCC support."""
    try:
        import torch.distributed as dist
        if hasattr(dist, 'is_ucc_available'):
            ucc_available = dist.is_ucc_available()
            print(f"✓ PyTorch UCC availability check: {ucc_available}")
            return True
        else:
            print("✗ PyTorch does not have is_ucc_available function")
            return False
    except ImportError as e:
        print(f"✗ Failed to import torch.distributed: {e}")
        return False

def test_backend_configuration():
    """Test that UCC backend is properly configured."""
    try:
        # Test environment variable configuration
        import os
        original_backend = os.environ.get('VLLM_ALL2ALL_BACKEND', None)
        
        # Set UCC backend
        os.environ['VLLM_ALL2ALL_BACKEND'] = 'ucc'
        
        # Import and check
        from vllm import envs
        
        # Check if 'ucc' is in the allowed choices by trying to set it
        backend = envs.VLLM_ALL2ALL_BACKEND
        print(f"✓ VLLM_ALL2ALL_BACKEND set to: {backend}")
        
        # Restore original value
        if original_backend is not None:
            os.environ['VLLM_ALL2ALL_BACKEND'] = original_backend
        else:
            os.environ.pop('VLLM_ALL2ALL_BACKEND', None)
        
        return True
    except Exception as e:
        print(f"✗ Backend configuration test failed: {e}")
        return False

def test_communicator_integration():
    """Test that communicators can handle UCC backend."""
    try:
        # Test CUDA communicator
        from vllm.distributed.device_communicators.cuda_communicator import CudaCommunicator
        import inspect
        
        cuda_source = inspect.getsource(CudaCommunicator.__init__)
        if 'UCCAll2AllManager' in cuda_source and 'ucc' in cuda_source:
            print("✓ CudaCommunicator has UCC integration")
        else:
            print("✗ CudaCommunicator missing UCC integration")
            return False
        
        # Test XPU communicator
        from vllm.distributed.device_communicators.xpu_communicator import XpuCommunicator
        xpu_source = inspect.getsource(XpuCommunicator.__init__)
        if 'UCCAll2AllManager' in xpu_source and 'ucc' in xpu_source:
            print("✓ XpuCommunicator has UCC integration")
        else:
            print("✗ XpuCommunicator missing UCC integration")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Communicator integration test failed: {e}")
        return False

def test_ucc_manager_interface():
    """Test that UCC manager has the required interface."""
    try:
        from vllm.distributed.device_communicators.all2all import UCCAll2AllManager
        from vllm.distributed.device_communicators.base_device_communicator import All2AllManagerBase
        
        # Check inheritance
        if issubclass(UCCAll2AllManager, All2AllManagerBase):
            print("✓ UCCAll2AllManager inherits from All2AllManagerBase")
        else:
            print("✗ UCCAll2AllManager does not inherit from All2AllManagerBase")
            return False
        
        # Check required methods
        required_methods = ['dispatch', 'combine', 'destroy']
        for method in required_methods:
            if hasattr(UCCAll2AllManager, method):
                print(f"✓ UCCAll2AllManager has {method} method")
            else:
                print(f"✗ UCCAll2AllManager missing {method} method")
                return False
        
        return True
    except Exception as e:
        print(f"✗ UCC manager interface test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Running UCC All2All Integration Tests")
    print("=" * 40)
    
    tests = [
        test_ucc_imports,
        test_torch_ucc_availability,
        test_backend_configuration,
        test_communicator_integration,
        test_ucc_manager_interface,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        if test():
            passed += 1
        else:
            print(f"Test {test.__name__} failed!")
    
    print("\n" + "=" * 40)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! UCC All2All integration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
