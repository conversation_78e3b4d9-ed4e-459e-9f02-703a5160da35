# UCC All2All Backend for Expert Parallelism

This document describes how to use the UCC (Unified Collective Communication) backend for all2all communication in VLLM's expert parallelism feature.

## Overview

UCC (Unified Collective Communication) is a high-performance collective communication library that provides optimized implementations for various collective operations across different hardware architectures (CPU, GPU, and DPU). VLLM's UCC All2All backend leverages PyTorch's built-in UCC support to provide efficient all2all communication for expert parallel models.

### Benefits of UCC Backend

- **Multi-node Performance**: UCC is particularly optimized for multi-node communication scenarios
- **Hardware Optimization**: Supports various hardware architectures and interconnects
- **Scalability**: Designed for large-scale distributed training workloads
- **Industry Standard**: Co-designed with industry partners for deep learning frameworks

## Prerequisites

### PyTorch with UCC Support

Ensure your PyTorch installation includes UCC backend support:

```python
import torch.distributed as dist
print(f"UCC available: {dist.is_ucc_available()}")
```

UCC backend is available in:
- PyTorch 2.3.06+ (NVIDIA containers)
- PyTorch builds with UCC compilation flags enabled

### UCC Library Installation

UCC backend requires the UCC library to be installed on your system. The library is typically included in:

- NVIDIA HPC-X toolkit
- NVIDIA PyTorch containers (23.06+)
- Custom PyTorch builds with UCC support

For manual installation, refer to the [UCC GitHub repository](https://github.com/openucx/ucc).

## Configuration

### Environment Variable

Set the all2all backend to use UCC:

```bash
export VLLM_ALL2ALL_BACKEND=ucc
```

### Available Backend Options

VLLM supports the following all2all backends:

- `naive`: Simple broadcast-based implementation (for testing)
- `allgather_reducescatter`: Default implementation using allgather/reducescatter
- `pplx`: PPLX kernels (requires pplx_kernels installation)
- `deepep_high_throughput`: DeepEP high-throughput kernels
- `deepep_low_latency`: DeepEP low-latency kernels
- `ucc`: UCC backend (this feature)

## Usage Examples

### Basic Usage

```python
import os
os.environ['VLLM_ALL2ALL_BACKEND'] = 'ucc'

from vllm import LLM, SamplingParams

# Initialize LLM with expert parallel model
llm = LLM(
    model="mistralai/Mixtral-8x7B-Instruct-v0.1",
    tensor_parallel_size=2,
    pipeline_parallel_size=1,
    # UCC backend will be used automatically for expert parallelism
)

prompts = ["Hello, how are you?"]
sampling_params = SamplingParams(temperature=0.8, top_p=0.95)
outputs = llm.generate(prompts, sampling_params)
```

### Multi-node Setup

For multi-node deployments, UCC backend provides optimized communication:

```bash
# Node 0
export VLLM_ALL2ALL_BACKEND=ucc
export MASTER_ADDR=node0.example.com
export MASTER_PORT=29500

torchrun --nproc_per_node=8 --nnodes=2 --node_rank=0 \
    --master_addr=node0.example.com --master_port=29500 \
    your_vllm_script.py

# Node 1
export VLLM_ALL2ALL_BACKEND=ucc
export MASTER_ADDR=node0.example.com
export MASTER_PORT=29500

torchrun --nproc_per_node=8 --nnodes=2 --node_rank=1 \
    --master_addr=node0.example.com --master_port=29500 \
    your_vllm_script.py
```

## Performance Considerations

### When to Use UCC Backend

UCC backend is recommended for:

- **Multi-node deployments**: UCC provides optimized inter-node communication
- **Large-scale training**: Better scalability for large numbers of nodes
- **High-bandwidth interconnects**: Takes advantage of specialized hardware
- **Mixed precision workloads**: Optimized for various data types

### Performance Tuning

1. **Network Configuration**: Ensure high-bandwidth, low-latency network (InfiniBand, NVLink)
2. **Process Placement**: Optimize process-to-GPU mapping for your hardware topology
3. **Memory Management**: Monitor GPU memory usage as UCC may have different memory patterns

### Benchmarking

Compare performance with other backends:

```bash
# Test with UCC backend
export VLLM_ALL2ALL_BACKEND=ucc
python benchmark_script.py

# Test with default backend
export VLLM_ALL2ALL_BACKEND=allgather_reducescatter
python benchmark_script.py
```

## Troubleshooting

### Common Issues

1. **UCC Not Available Error**
   ```
   RuntimeError: UCC backend is not available. Please ensure PyTorch is built with UCC support
   ```
   - Solution: Use PyTorch with UCC support or switch to a different backend

2. **Process Group Creation Failed**
   ```
   RuntimeError: Failed to create UCC process group
   ```
   - Check UCC library installation
   - Verify network configuration
   - Ensure proper distributed setup

3. **Performance Issues**
   - Verify network bandwidth and latency
   - Check process placement and affinity
   - Monitor GPU memory usage

### Debugging

Enable verbose logging:

```bash
export TORCH_DISTRIBUTED_DEBUG=DETAIL
export VLLM_ALL2ALL_BACKEND=ucc
```

### Fallback Options

If UCC backend is not available, VLLM will automatically fall back to other backends based on availability:

1. `allgather_reducescatter` (default)
2. `naive` (always available)

## Implementation Details

### Architecture

The UCC All2All manager (`UCCAll2AllManager`) extends the base `All2AllManagerBase` class and implements:

- `dispatch()`: Distributes tokens across expert parallel ranks using UCC-optimized allgather
- `combine()`: Aggregates results using UCC-optimized reduce-scatter
- `destroy()`: Cleans up UCC process group resources

### Integration Points

UCC backend is integrated into:

- `CudaCommunicator`: For NVIDIA GPU deployments
- `XpuCommunicator`: For Intel XPU deployments
- Environment configuration system
- Automatic backend selection logic

## References

- [UCC GitHub Repository](https://github.com/openucx/ucc)
- [PyTorch Distributed Documentation](https://pytorch.org/docs/stable/distributed.html)
- [NVIDIA HPC-X Toolkit](https://developer.nvidia.com/networking/hpc-x)
- [VLLM Expert Parallelism Documentation](../distributed/expert_parallelism.md)
